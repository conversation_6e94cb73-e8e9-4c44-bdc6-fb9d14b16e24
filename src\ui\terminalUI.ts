import chalk from 'chalk';

/**
 * Renders the agent response in the terminal
 */
export async function renderAgentResponse(content: string): Promise<void> {
  if (!content) return;
  
  // Split the content by newlines for better formatting
  const lines = content.split('\n');
  
  console.log('\n' + chalk.blue.bold('Agent: '));
  
  // Print each line with proper formatting
  for (const line of lines) {
    // Detect code blocks
    if (line.startsWith('```')) {
      // Skip the opening/closing code block markers
      continue;
    } else if (line.trim() === '```') {
      // End of code block
      continue;
    } else {
      // Regular text
      console.log(line);
    }
  }
  
  console.log(); // Add a blank line for better readability
}

/**
 * Renders streaming content in the terminal
 * This is optimized for displaying content as it arrives
 */
export function renderStreamingContent(content: string): void {
  // Check for code block tokens
  if (content.includes('```')) {
    // Special handling for code blocks will be needed in a real implementation
    // For now, just print the content
    process.stdout.write(content);
  } else {
    // For regular text, just write it directly
    process.stdout.write(content);
  }
}

/**
 * Renders a message indicating tool execution
 */
export function renderToolExecution(toolName: string): void {
  process.stdout.write(chalk.yellow(`→ Executing: ${toolName}... `));
}

/**
 * Clears the current line in the terminal
 */
export function clearLine(): void {
  process.stdout.write('\r                                                                      \r');
}

/**
 * Renders a diff view for file changes
 */
export function renderDiff(originalContent: string, newContent: string, fileName: string): void {
  console.log('\n' + chalk.yellow.bold(`Changes to ${fileName}:`));
  
  // Simple diff implementation for now
  // In a real implementation, this would use a proper diff algorithm
  
  const originalLines = originalContent.split('\n');
  const newLines = newContent.split('\n');
  
  for (let i = 0; i < Math.max(originalLines.length, newLines.length); i++) {
    const originalLine = originalLines[i] || '';
    const newLine = newLines[i] || '';
    
    if (originalLine !== newLine) {
      if (originalLine) {
        console.log(chalk.red(`- ${originalLine}`));
      }
      if (newLine) {
        console.log(chalk.green(`+ ${newLine}`));
      }
    } else {
      console.log(`  ${originalLine}`);
    }
  }
  
  console.log(); // Add a blank line for better readability
}

/**
 * Renders a loading spinner in the terminal
 */
export class LoadingSpinner {
  private frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
  private interval: NodeJS.Timeout | null = null;
  private currentFrame = 0;
  private text: string;
  private spinning = false;
  
  constructor(text: string = 'Processing') {
    this.text = text;
  }
  
  start(): void {
    if (this.interval) return;
    
    this.spinning = true;
    
    // Clear the current line
    process.stdout.write('\r');
    
    this.interval = setInterval(() => {
      const frame = this.frames[this.currentFrame];
      process.stdout.write(`\r${chalk.blue(frame)} ${this.text}...`);
      this.currentFrame = (this.currentFrame + 1) % this.frames.length;
    }, 80);
  }
  
  stop(): void {
    if (!this.interval) return;
    
    clearInterval(this.interval);
    this.interval = null;
    this.spinning = false;
    
    // Clear the spinner
    process.stdout.write('\r                                        \r');
  }
  
  updateText(text: string): void {
    this.text = text;
  }
  
  isSpinning(): boolean {
    return this.spinning;
  }
}

/**
 * Renders a success message
 */
export function renderSuccess(message: string): void {
  console.log(chalk.green(`✓ ${message}`));
}

/**
 * Renders an error message
 */
export function renderError(message: string): void {
  console.log(chalk.red(`✗ ${message}`));
}

/**
 * Renders a warning message
 */
export function renderWarning(message: string): void {
  console.log(chalk.yellow(`⚠ ${message}`));
}

/**
 * Renders a semantic code search result
 */
export function renderSearchResult(fileName: string, lineNumber: number, snippet: string, relevance: number): void {
  console.log(chalk.cyan(`File: ${fileName}:${lineNumber}`));
  console.log(chalk.gray('----------------------------------------'));
  console.log(snippet);
  console.log(chalk.gray('----------------------------------------'));
  console.log(chalk.yellow(`Relevance: ${(relevance * 100).toFixed(1)}%\n`));
} 