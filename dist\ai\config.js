import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';
import { z } from 'zod';
// Config schema
export const ConfigSchema = z.object({
    provider: z.enum(['openai', 'deepseek', 'ollama']),
    apiKey: z.string().optional(),
    baseURL: z.string().optional(),
    model: z.string(),
});
// Default configs for each provider
const DEFAULT_CONFIGS = {
    openai: {
        baseURL: 'https://api.openai.com/v1',
        model: 'gpt-4o',
    },
    deepseek: {
        baseURL: 'https://api.deepseek.com/v1',
        model: 'deepseek-coder',
    },
    ollama: {
        baseURL: 'http://localhost:11434/v1',
        model: 'llama3',
    },
};
/**
 * Path to the config file
 */
function getConfigPath() {
    return path.join(os.homedir(), '.agent-cli', 'config.json');
}
/**
 * Saves the config to disk
 */
export async function saveConfig(config) {
    try {
        // Validate the config
        ConfigSchema.parse(config);
        // Create the directory if it doesn't exist
        const configDir = path.dirname(getConfigPath());
        await fs.mkdir(configDir, { recursive: true });
        // Write the config file
        await fs.writeFile(getConfigPath(), JSON.stringify(config, null, 2));
    }
    catch (error) {
        console.error('Error saving config:', error);
        throw error;
    }
}
/**
 * Gets the current config, loading it from disk or creating a default
 */
export async function getConfig() {
    try {
        // Try to read the config file
        const configData = await fs.readFile(getConfigPath(), 'utf-8');
        const config = JSON.parse(configData);
        // Validate the config
        return ConfigSchema.parse(config);
    }
    catch (error) {
        // If the file doesn't exist or is invalid, return a default config
        console.log('No config found, using default OpenAI config');
        const defaultConfig = {
            provider: 'openai',
            ...DEFAULT_CONFIGS.openai,
            apiKey: process.env.OPENAI_API_KEY,
        };
        // Save the default config
        await saveConfig(defaultConfig);
        return defaultConfig;
    }
}
/**
 * Updates the provider configuration
 */
export async function updateProviderConfig(provider, apiKey, baseURL, model) {
    // Get the current config
    const currentConfig = await getConfig();
    // Create the new config
    const newConfig = {
        ...currentConfig,
        provider,
        apiKey: apiKey || currentConfig.apiKey,
        baseURL: baseURL || DEFAULT_CONFIGS[provider].baseURL,
        model: model || DEFAULT_CONFIGS[provider].model,
    };
    // Save the new config
    await saveConfig(newConfig);
    return newConfig;
}
//# sourceMappingURL=config.js.map