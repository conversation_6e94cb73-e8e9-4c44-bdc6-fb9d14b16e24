import { ToolDefinition } from './types.js';
import { getShellCommandTool } from './shellCommandTool.js';
import { getFileOperationTools } from './fileOperationTools.js';
import { semanticSearchTool } from './semanticSearchTool.js';
import { gitOperationsTool } from './gitOperationsTool.js';
import { debuggingTool } from './debuggingTool.js';

// Map of available tools
const toolMap = new Map<string, ToolDefinition>();

/**
 * Registers a tool in the registry
 */
export function registerTool(tool: ToolDefinition): void {
  toolMap.set(tool.name, tool);
}

/**
 * Gets a tool by name
 */
export function getTool(name: string): ToolDefinition | undefined {
  return toolMap.get(name);
}

/**
 * Gets all available tools
 */
export async function getAvailableTools(): Promise<ToolDefinition[]> {
  // If no tools are registered yet, register the default tools
  if (toolMap.size === 0) {
    await registerDefaultTools();
  }
  
  return Array.from(toolMap.values());
}

/**
 * Registers the default tools
 */
async function registerDefaultTools(): Promise<void> {
  // Register shell command tool
  registerTool(getShellCommandTool());
  
  // Register file operation tools
  const fileTools = await getFileOperationTools();
  fileTools.forEach(tool => registerTool(tool));
  
  // Register semantic search tool
  registerTool(semanticSearchTool);
  
  // Register git operations tool
  registerTool(gitOperationsTool);
  
  // Register debugging tool
  registerTool(debuggingTool);
  
  // Additional tools would be registered here
  
  console.log(`Registered ${toolMap.size} tools`);
} 