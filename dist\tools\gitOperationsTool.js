import { exec } from 'child_process';
import { promisify } from 'util';
import chalk from 'chalk';
const execAsync = promisify(exec);
/**
 * Git operations tool for common git tasks
 */
export const gitOperationsTool = {
    name: 'gitOperations',
    description: 'Perform git operations like commit, branch, status, and more',
    parameters: {
        type: 'object',
        properties: {
            operation: {
                type: 'string',
                description: 'The git operation to perform',
                enum: [
                    'status',
                    'add',
                    'commit',
                    'branch',
                    'checkout',
                    'pull',
                    'push',
                    'log',
                    'diff',
                    'merge'
                ]
            },
            files: {
                type: 'string',
                description: 'Files to include in the operation (for add, etc.)'
            },
            message: {
                type: 'string',
                description: 'Commit message (for commit operation)'
            },
            branchName: {
                type: 'string',
                description: 'Branch name (for branch, checkout, merge operations)'
            },
            createBranch: {
                type: 'boolean',
                description: 'Whether to create a new branch (for checkout operation)'
            },
            remote: {
                type: 'string',
                description: 'Remote name (for push, pull operations)'
            }
        },
        required: ['operation']
    },
    async execute(args) {
        try {
            // Check if git is installed
            try {
                await execAsync('git --version');
            }
            catch (error) {
                return {
                    success: false,
                    message: 'Git is not installed or not available in PATH'
                };
            }
            // Check if we're in a git repository
            try {
                await execAsync('git rev-parse --is-inside-work-tree');
            }
            catch (error) {
                return {
                    success: false,
                    message: 'Not inside a git repository'
                };
            }
            // Execute the requested operation
            switch (args.operation) {
                case 'status':
                    return await gitStatus();
                case 'add':
                    return await gitAdd(args.files || '.');
                case 'commit':
                    if (!args.message) {
                        return {
                            success: false,
                            message: 'Commit message is required'
                        };
                    }
                    return await gitCommit(args.message);
                case 'branch':
                    return await gitBranch(args.branchName);
                case 'checkout':
                    if (!args.branchName) {
                        return {
                            success: false,
                            message: 'Branch name is required for checkout'
                        };
                    }
                    return await gitCheckout(args.branchName, args.createBranch || false);
                case 'pull':
                    return await gitPull(args.remote || 'origin', args.branchName);
                case 'push':
                    return await gitPush(args.remote || 'origin', args.branchName);
                case 'log':
                    return await gitLog();
                case 'diff':
                    return await gitDiff(args.files);
                case 'merge':
                    if (!args.branchName) {
                        return {
                            success: false,
                            message: 'Branch name is required for merge'
                        };
                    }
                    return await gitMerge(args.branchName);
                default:
                    return {
                        success: false,
                        message: `Unknown git operation: ${args.operation}`
                    };
            }
        }
        catch (error) {
            console.error(chalk.red('Error in git operation:'), error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
};
/**
 * Get git status
 */
async function gitStatus() {
    const { stdout } = await execAsync('git status');
    return {
        success: true,
        result: stdout.trim()
    };
}
/**
 * Add files to git staging
 */
async function gitAdd(files) {
    const { stdout } = await execAsync(`git add ${files}`);
    return {
        success: true,
        result: stdout.trim() || `Added ${files} to staging area`
    };
}
/**
 * Commit changes
 */
async function gitCommit(message) {
    const { stdout } = await execAsync(`git commit -m "${message}"`);
    return {
        success: true,
        result: stdout.trim()
    };
}
/**
 * List or create branches
 */
async function gitBranch(branchName) {
    if (branchName) {
        const { stdout } = await execAsync(`git branch ${branchName}`);
        return {
            success: true,
            result: stdout.trim() || `Created branch ${branchName}`
        };
    }
    else {
        const { stdout } = await execAsync('git branch');
        return {
            success: true,
            result: stdout.trim()
        };
    }
}
/**
 * Checkout a branch
 */
async function gitCheckout(branchName, createBranch) {
    const createFlag = createBranch ? '-b' : '';
    const { stdout } = await execAsync(`git checkout ${createFlag} ${branchName}`);
    return {
        success: true,
        result: stdout.trim() || `Switched to ${createBranch ? 'new ' : ''}branch ${branchName}`
    };
}
/**
 * Pull changes from remote
 */
async function gitPull(remote, branch) {
    const branchArg = branch ? ` ${branch}` : '';
    const { stdout } = await execAsync(`git pull ${remote}${branchArg}`);
    return {
        success: true,
        result: stdout.trim()
    };
}
/**
 * Push changes to remote
 */
async function gitPush(remote, branch) {
    const branchArg = branch ? ` ${branch}` : '';
    const { stdout } = await execAsync(`git push ${remote}${branchArg}`);
    return {
        success: true,
        result: stdout.trim()
    };
}
/**
 * Get git log
 */
async function gitLog() {
    const { stdout } = await execAsync('git log --oneline -n 10');
    return {
        success: true,
        result: stdout.trim()
    };
}
/**
 * Get git diff
 */
async function gitDiff(files) {
    const fileArgs = files ? ` -- ${files}` : '';
    const { stdout } = await execAsync(`git diff${fileArgs}`);
    return {
        success: true,
        result: stdout.trim()
    };
}
/**
 * Merge a branch
 */
async function gitMerge(branchName) {
    const { stdout } = await execAsync(`git merge ${branchName}`);
    return {
        success: true,
        result: stdout.trim() || `Merged ${branchName} into current branch`
    };
}
/**
 * Register the git operations tool in the tool registry
 */
export function registerGitOperationsTool() {
    // This would be called in the tool registry initialization
    // toolRegistry.register(gitOperationsTool);
}
//# sourceMappingURL=gitOperationsTool.js.map