import OpenAI from 'openai';
import { ToolDefinition } from '../tools/types.js';
export interface AgentResponse {
    content: string;
    toolCalls?: any[];
}
export interface GenerateResponseParams {
    input: string;
    context: any;
    tools: ToolDefinition[];
    previousToolResult?: {
        toolCall: any;
        result: any;
    };
}
export interface StreamCallbacks {
    onContent?: (content: string) => void;
    onToolCall?: (toolCall: any) => void;
    onError?: (error: Error) => void;
    onComplete?: (response: AgentResponse) => void;
}
/**
 * Creates an OpenAI client with the configured settings
 */
export declare function createAIClient(): Promise<OpenAI>;
/**
 * Generates a response from the AI agent
 */
export declare function generateAgentResponse(params: GenerateResponseParams, streamCallbacks?: StreamCallbacks): Promise<AgentResponse>;
