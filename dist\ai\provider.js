import OpenAI from 'openai';
import { getConfig } from './config.js';
import chalk from 'chalk';
/**
 * Creates an OpenAI client with the configured settings
 */
export async function createAIClient() {
    const config = await getConfig();
    return new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseURL
    });
}
/**
 * Generates a response from the AI agent
 */
export async function generateAgentResponse(params, streamCallbacks) {
    const config = await getConfig();
    try {
        // Create the OpenAI client
        const openai = await createAIClient();
        // Convert tool definitions to OpenAI tool format
        const openaiTools = params.tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters
            }
        }));
        // Build the messages array
        const messages = buildMessages(params);
        // Use streaming if callbacks are provided, otherwise use regular request
        if (streamCallbacks) {
            return await handleStreamingResponse(openai, messages, openaiTools, config.model, streamCallbacks);
        }
        else {
            // Call the OpenAI API
            const response = await openai.chat.completions.create({
                model: config.model,
                messages,
                tools: openaiTools,
                temperature: 0.7,
                stream: false
            });
            // Get the response message
            const responseMessage = response.choices[0].message;
            // Return formatted response
            return {
                content: responseMessage.content || '',
                toolCalls: responseMessage.tool_calls
            };
        }
    }
    catch (error) {
        console.error(chalk.red('Error generating agent response:'), error);
        if (streamCallbacks?.onError) {
            streamCallbacks.onError(error instanceof Error ? error : new Error(String(error)));
        }
        throw error;
    }
}
/**
 * Handles streaming response from the AI
 */
async function handleStreamingResponse(openai, messages, tools, model, callbacks) {
    // Create accumulators for the response
    let contentAccumulator = '';
    let toolCallsAccumulator = [];
    let currentToolCall = null;
    try {
        // Create the streaming request
        const stream = await openai.chat.completions.create({
            model,
            messages,
            tools,
            temperature: 0.7,
            stream: true
        });
        // Process the stream
        for await (const chunk of stream) {
            // Process the delta content
            if (chunk.choices[0]?.delta?.content) {
                const content = chunk.choices[0].delta.content;
                contentAccumulator += content;
                // Call the content callback
                if (callbacks.onContent) {
                    callbacks.onContent(content);
                }
            }
            // Process tool calls
            if (chunk.choices[0]?.delta?.tool_calls) {
                for (const toolCallDelta of chunk.choices[0].delta.tool_calls) {
                    // Initialize a new tool call if we got an index
                    if (toolCallDelta.index !== undefined) {
                        if (!toolCallsAccumulator[toolCallDelta.index]) {
                            toolCallsAccumulator[toolCallDelta.index] = {
                                id: '',
                                type: 'function',
                                function: {
                                    name: '',
                                    arguments: ''
                                }
                            };
                            currentToolCall = toolCallsAccumulator[toolCallDelta.index];
                        }
                    }
                    // Update the tool call ID
                    if (toolCallDelta.id) {
                        currentToolCall.id += toolCallDelta.id;
                    }
                    // Update the function name
                    if (toolCallDelta.function?.name) {
                        currentToolCall.function.name += toolCallDelta.function.name;
                    }
                    // Update the function arguments
                    if (toolCallDelta.function?.arguments) {
                        currentToolCall.function.arguments += toolCallDelta.function.arguments;
                        // Try to parse the arguments as JSON if they look complete
                        try {
                            const args = JSON.parse(currentToolCall.function.arguments);
                            // If parsing succeeded and we have callbacks, call the tool call callback
                            if (callbacks.onToolCall) {
                                const toolCall = {
                                    id: currentToolCall.id,
                                    name: currentToolCall.function.name,
                                    arguments: args
                                };
                                callbacks.onToolCall(toolCall);
                            }
                        }
                        catch (e) {
                            // Arguments JSON is incomplete, continue accumulating
                        }
                    }
                }
            }
        }
        // Create the final response
        const finalResponse = {
            content: contentAccumulator,
            toolCalls: toolCallsAccumulator.length > 0 ? toolCallsAccumulator : undefined
        };
        // Call the complete callback
        if (callbacks.onComplete) {
            callbacks.onComplete(finalResponse);
        }
        return finalResponse;
    }
    catch (error) {
        console.error(chalk.red('Error in streaming response:'), error);
        if (callbacks.onError) {
            callbacks.onError(error instanceof Error ? error : new Error(String(error)));
        }
        throw error;
    }
}
/**
 * Builds the messages array for the OpenAI API
 */
function buildMessages(params) {
    const messages = [
        {
            role: 'system',
            content: `You are a fully autonomous agent that operates in the user's local environment. You have access to tools that allow you to interact with the file system, run shell commands, search code semantically, perform git operations, and debug code.

Your goal is to complete tasks without requiring user confirmation or approval. When you identify a task that needs to be done:
1. Execute it immediately using the appropriate tools
2. Provide clear explanations of what you're doing
3. Show the results of your actions
4. Continue with follow-up actions as needed until the task is complete

You should always use the most appropriate tool for each task and complete tasks fully without asking for confirmation. Think step-by-step and be thorough in your approach.`
        },
        {
            role: 'user',
            content: params.input
        }
    ];
    // Add previous conversation context if available
    if (params.context.conversationHistory && params.context.conversationHistory.length > 0) {
        // Insert conversation history before the current user message
        const historyMessages = params.context.conversationHistory.map((msg) => ({
            role: msg.role,
            content: msg.content,
            // Include tool calls if this was an assistant message with tool calls
            ...(msg.toolCalls && { tool_calls: msg.toolCalls })
        }));
        // Insert history before the current user message
        messages.splice(1, 0, ...historyMessages);
    }
    // Add tool result if available
    if (params.previousToolResult) {
        messages.push({
            role: 'tool',
            content: JSON.stringify(params.previousToolResult.result),
            tool_call_id: params.previousToolResult.toolCall.id
        }); // Using type assertion to avoid TypeScript error
    }
    return messages;
}
//# sourceMappingURL=provider.js.map