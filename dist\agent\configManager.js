import { select, input, confirm } from '@inquirer/prompts';
import { updateProviderConfig, getConfig } from '../ai/config.js';
import chalk from 'chalk';
/**
 * Sets up the agent configuration using command-line options or interactive prompts
 */
export async function setupAgentConfig(options) {
    try {
        // Get current configuration if it exists
        const currentConfig = await getConfig().catch(() => null);
        // Determine the provider
        const provider = await determineProvider(options.provider, currentConfig?.provider);
        // Get the API key for the provider
        const apiKey = await determineApiKey(options.apiKey, provider, currentConfig?.apiKey);
        // Get the model for the provider
        const model = await determineModel(options.model, provider, currentConfig?.model);
        // Get the base URL for the provider
        const baseUrl = await determineBaseUrl(options.baseUrl, provider, currentConfig?.baseURL);
        // Save the configuration
        await updateProviderConfig(provider, apiKey, baseUrl, model);
        console.log(chalk.green(`\nConfiguration saved for ${provider}!`));
        console.log(chalk.cyan(`Provider: ${provider}`));
        console.log(chalk.cyan(`Model: ${model}`));
        if (provider === 'ollama') {
            console.log(chalk.yellow('\nReminder: Make sure Ollama is running locally before using the agent.'));
            console.log(chalk.yellow('You can download Ollama from https://ollama.com\n'));
        }
    }
    catch (error) {
        console.error(chalk.red('Error setting up configuration:'), error);
        throw error;
    }
}
/**
 * Determines the provider to use
 */
async function determineProvider(optionsProvider, currentProvider) {
    if (optionsProvider) {
        return optionsProvider;
    }
    console.log(chalk.blue('\nSelect AI Provider:'));
    return await select({
        message: 'Choose an AI provider:',
        choices: [
            { name: 'OpenAI (GPT-4o, etc.)', value: 'openai' },
            { name: 'Deepseek (Deepseek Coder, etc.)', value: 'deepseek' },
            { name: 'Ollama (Local models like Llama3)', value: 'ollama' }
        ],
        default: currentProvider || 'openai'
    });
}
/**
 * Determines the API key to use
 */
async function determineApiKey(optionsApiKey, provider, currentApiKey) {
    if (optionsApiKey) {
        return optionsApiKey;
    }
    // For Ollama, API key is not required
    if (provider === 'ollama') {
        return undefined;
    }
    console.log(chalk.blue(`\nAPI Key for ${provider}:`));
    // Check for API key in environment variables
    const envApiKey = process.env[`${provider?.toUpperCase()}_API_KEY`];
    if (envApiKey) {
        const useEnvKey = await confirm({
            message: `Use API key from ${provider?.toUpperCase()}_API_KEY environment variable?`,
            default: true
        });
        if (useEnvKey) {
            return envApiKey;
        }
    }
    // Ask for API key
    return await input({
        message: `Enter your ${provider} API key:`,
        default: currentApiKey,
        validate: (value) => value.length > 0 ? true : 'API key is required'
    });
}
/**
 * Determines the model to use
 */
async function determineModel(optionsModel, provider, currentModel) {
    if (optionsModel) {
        return optionsModel;
    }
    console.log(chalk.blue(`\nModel Selection for ${provider}:`));
    const modelChoices = getModelChoicesForProvider(provider);
    const defaultModel = currentModel || modelChoices[0].value;
    return await select({
        message: 'Choose a model:',
        choices: modelChoices,
        default: defaultModel
    });
}
/**
 * Gets model choices for a provider
 */
function getModelChoicesForProvider(provider) {
    switch (provider) {
        case 'openai':
            return [
                { name: 'GPT-4o', value: 'gpt-4o' },
                { name: 'GPT-4o-mini', value: 'gpt-4o-mini' },
                { name: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' }
            ];
        case 'deepseek':
            return [
                { name: 'Deepseek Coder', value: 'deepseek-coder' },
                { name: 'Deepseek Chat', value: 'deepseek-chat' }
            ];
        case 'ollama':
            return [
                { name: 'Llama3', value: 'llama3' },
                { name: 'CodeLlama', value: 'codellama' },
                { name: 'Mistral', value: 'mistral' },
                { name: 'Other (specify)', value: 'custom' }
            ];
        default:
            return [
                { name: 'Default model', value: 'default' }
            ];
    }
}
/**
 * Determines the base URL to use
 */
async function determineBaseUrl(optionsBaseUrl, provider, currentBaseUrl) {
    if (optionsBaseUrl) {
        return optionsBaseUrl;
    }
    // Get the default base URL for the provider
    const defaultBaseUrl = getDefaultBaseUrlForProvider(provider);
    // Use the default base URL if not custom
    const customBaseUrl = await confirm({
        message: 'Use custom base URL for API?',
        default: false
    });
    if (!customBaseUrl) {
        return defaultBaseUrl;
    }
    return await input({
        message: `Enter custom base URL for ${provider}:`,
        default: currentBaseUrl || defaultBaseUrl
    });
}
/**
 * Gets the default base URL for a provider
 */
function getDefaultBaseUrlForProvider(provider) {
    switch (provider) {
        case 'openai':
            return 'https://api.openai.com/v1';
        case 'deepseek':
            return 'https://api.deepseek.com/v1';
        case 'ollama':
            return 'http://localhost:11434/v1';
        default:
            return 'https://api.openai.com/v1';
    }
}
//# sourceMappingURL=configManager.js.map