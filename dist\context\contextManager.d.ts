import { AgentSession } from '../agent/sessionManager.js';
/**
 * Manages the context for the agent
 */
export declare class ContextManager {
    private session;
    constructor(session: AgentSession);
    /**
     * Gets the current context for the agent
     */
    getCurrentContext(): Promise<any>;
    /**
     * Updates the context with new information
     */
    updateContext(update: {
        toolCall?: any;
        toolResult?: any;
        message?: {
            role: string;
            content: string;
        };
    }): Promise<void>;
    /**
     * Gets information about the system
     */
    private getSystemInfo;
}
