/**
 * Represents a tool definition that can be used by the agent
 */
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: any;
    execute: (args: any) => Promise<any>;
}
/**
 * Represents a tool call from the AI
 */
export interface ToolCall {
    id: string;
    name: string;
    arguments: any;
}
/**
 * Represents the result of a tool execution
 */
export interface ToolResult {
    success: boolean;
    result: any;
    error?: string;
}
