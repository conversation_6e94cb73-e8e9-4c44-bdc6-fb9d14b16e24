#!/usr/bin/env node
import { Command } from 'commander';
import { startAgent } from './agent/agentEngine.js';
import { setupAgentConfig } from './agent/configManager.js';
import chalk from 'chalk';
const program = new Command();
program
    .name('agent')
    .description('Autonomous Agent CLI Tool')
    .version('0.1.0');
program
    .command('run')
    .description('Run the autonomous agent with a prompt')
    .argument('<prompt>', 'The prompt to send to the agent')
    .action(async (prompt) => {
    try {
        await startAgent(prompt);
    }
    catch (error) {
        console.error(chalk.red('Error:'), error);
        process.exit(1);
    }
});
program
    .command('init')
    .description('Initialize the agent with configuration')
    .option('-p, --provider <provider>', 'The AI provider to use (openai, deepseek, ollama)')
    .option('-k, --api-key <key>', 'The API key for the provider')
    .option('-m, --model <model>', 'The model to use')
    .option('-u, --base-url <url>', 'Custom base URL for the provider API')
    .action(async (options) => {
    try {
        console.log(chalk.blue('Initializing agent configuration...'));
        await setupAgentConfig(options);
        console.log(chalk.green('Configuration complete!'));
    }
    catch (error) {
        console.error(chalk.red('Error:'), error);
        process.exit(1);
    }
});
program.parse();
//# sourceMappingURL=index.js.map