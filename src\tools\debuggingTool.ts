import { ToolDefinition } from './types.js';
import * as fs from 'fs/promises';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import chalk from 'chalk';

const execAsync = promisify(exec);

/**
 * Debugging tool for analyzing and fixing common issues in code
 */
export const debuggingTool: ToolDefinition = {
  name: 'debugCode',
  description: 'Analyze and fix common issues in code files',
  parameters: {
    type: 'object',
    properties: {
      filePath: {
        type: 'string',
        description: 'Path to the file to debug'
      },
      operation: {
        type: 'string',
        description: 'The debugging operation to perform',
        enum: [
          'analyze',
          'lint',
          'fix',
          'test'
        ]
      },
      fixDescription: {
        type: 'string',
        description: 'Description of the fix to apply (for fix operation)'
      }
    },
    required: ['filePath', 'operation']
  },
  
  async execute(args: {
    filePath: string;
    operation: string;
    fixDescription?: string;
  }): Promise<any> {
    try {
      const filePath = path.resolve(process.cwd(), args.filePath);
      
      // Check if the file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        return {
          success: false,
          message: `File not found: ${args.filePath}`
        };
      }
      
      // Execute the requested operation
      switch (args.operation) {
        case 'analyze':
          return await analyzeCode(filePath);
          
        case 'lint':
          return await lintCode(filePath);
          
        case 'fix':
          if (!args.fixDescription) {
            return {
              success: false,
              message: 'Fix description is required for fix operation'
            };
          }
          return await fixCode(filePath, args.fixDescription);
          
        case 'test':
          return await testCode(filePath);
          
        default:
          return {
            success: false,
            message: `Unknown debugging operation: ${args.operation}`
          };
      }
    } catch (error) {
      console.error(chalk.red('Error in debugging operation:'), error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
};

/**
 * Analyze code for potential issues
 */
async function analyzeCode(filePath: string): Promise<any> {
  try {
    // Read the file content
    const content = await fs.readFile(filePath, 'utf-8');
    const fileExtension = path.extname(filePath).toLowerCase();
    
    // Results object
    const results = {
      issues: [] as any[],
      suggestions: [] as string[]
    };
    
    // Check for common issues based on file type
    if (fileExtension === '.js' || fileExtension === '.ts') {
      // Check for console.log statements
      const consoleLogMatches = content.match(/console\.log\(/g);
      if (consoleLogMatches) {
        results.issues.push({
          type: 'debug_code',
          message: `Found ${consoleLogMatches.length} console.log statements that might need to be removed in production`,
          count: consoleLogMatches.length
        });
      }
      
      // Check for TODO comments
      const todoMatches = content.match(/\/\/\s*TODO|\/\*\s*TODO/g);
      if (todoMatches) {
        results.issues.push({
          type: 'todo',
          message: `Found ${todoMatches.length} TODO comments that might need attention`,
          count: todoMatches.length
        });
      }
      
      // Check for potentially unused variables (simple check)
      const varDeclarations = content.match(/(?:const|let|var)\s+([a-zA-Z0-9_]+)/g);
      if (varDeclarations) {
        for (const declaration of varDeclarations) {
          const varName = declaration.replace(/(?:const|let|var)\s+/, '');
          const varUsage = new RegExp(`[^a-zA-Z0-9_]${varName}[^a-zA-Z0-9_]`, 'g');
          const usageCount = (content.match(varUsage) || []).length;
          
          if (usageCount <= 1) { // Only the declaration
            results.issues.push({
              type: 'unused_variable',
              message: `Variable ${varName} might be unused`,
              variable: varName
            });
          }
        }
      }
      
      // Add suggestions
      if (results.issues.length > 0) {
        results.suggestions.push('Consider running a linter to identify and fix issues');
        results.suggestions.push('Review TODO comments and address them if needed');
        results.suggestions.push('Remove debug console.log statements before production');
      }
    }
    
    return {
      success: true,
      result: {
        filePath,
        fileType: fileExtension,
        issueCount: results.issues.length,
        issues: results.issues,
        suggestions: results.suggestions
      }
    };
  } catch (error) {
    console.error(`Error analyzing ${filePath}:`, error);
    throw error;
  }
}

/**
 * Lint code using available linters
 */
async function lintCode(filePath: string): Promise<any> {
  try {
    const fileExtension = path.extname(filePath).toLowerCase();
    let lintCommand = '';
    
    // Determine the appropriate linter based on file extension
    if (fileExtension === '.js' || fileExtension === '.ts') {
      // Check if eslint is available
      try {
        await execAsync('npx eslint --version');
        lintCommand = `npx eslint "${filePath}" --format json`;
      } catch {
        // ESLint not available, try TypeScript compiler for .ts files
        if (fileExtension === '.ts') {
          try {
            await execAsync('npx tsc --version');
            lintCommand = `npx tsc "${filePath}" --noEmit`;
          } catch {
            return {
              success: false,
              message: 'No suitable linter found. Please install eslint or typescript.'
            };
          }
        } else {
          return {
            success: false,
            message: 'ESLint not found. Please install eslint to lint JavaScript files.'
          };
        }
      }
    } else {
      return {
        success: false,
        message: `No linter available for ${fileExtension} files.`
      };
    }
    
    // Run the linting command
    try {
      const { stdout } = await execAsync(lintCommand);
      
      // Parse the results
      let lintResults;
      if (lintCommand.includes('--format json')) {
        lintResults = JSON.parse(stdout);
      } else {
        lintResults = { output: stdout };
      }
      
      return {
        success: true,
        result: {
          filePath,
          lintResults
        }
      };
    } catch (error) {
      // For tsc, an error means there were type issues
      if (lintCommand.includes('tsc') && error instanceof Error) {
        return {
          success: true,
          result: {
            filePath,
            lintResults: {
              errors: (error as any).stderr || (error as any).message
            }
          }
        };
      }
      
      // For eslint, we might still get JSON output with errors
      if (lintCommand.includes('eslint') && error instanceof Error && (error as any).stdout) {
        try {
          const lintResults = JSON.parse((error as any).stdout);
          return {
            success: true,
            result: {
              filePath,
              lintResults
            }
          };
        } catch {
          // Couldn't parse the output
        }
      }
      
      throw error;
    }
  } catch (error) {
    console.error(`Error linting ${filePath}:`, error);
    throw error;
  }
}

/**
 * Fix code based on description
 * This is a placeholder for a more sophisticated implementation
 */
async function fixCode(filePath: string, fixDescription: string): Promise<any> {
  try {
    // Read the file content
    const content = await fs.readFile(filePath, 'utf-8');
    
    // For now, we'll just return the content and the fix description
    // A real implementation would use AI to generate fixes
    return {
      success: true,
      result: {
        filePath,
        content,
        fixDescription,
        message: 'To implement fixes, the agent would analyze the code and apply changes based on the fix description.'
      }
    };
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error);
    throw error;
  }
}

/**
 * Run tests related to the file
 */
async function testCode(filePath: string): Promise<any> {
  try {
    const fileExtension = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath, fileExtension);
    const directory = path.dirname(filePath);
    
    // Look for test files related to this file
    const testFilePatterns = [
      path.join(directory, `${fileName}.test${fileExtension}`),
      path.join(directory, `${fileName}.spec${fileExtension}`),
      path.join(directory, '__tests__', `${fileName}.test${fileExtension}`),
      path.join(directory, '__tests__', `${fileName}.spec${fileExtension}`),
      path.join(process.cwd(), 'tests', `${fileName}.test${fileExtension}`),
      path.join(process.cwd(), 'tests', `${fileName}.spec${fileExtension}`)
    ];
    
    // Find existing test files
    const testFiles = [];
    for (const testPattern of testFilePatterns) {
      try {
        await fs.access(testPattern);
        testFiles.push(testPattern);
      } catch {
        // File doesn't exist, continue
      }
    }
    
    if (testFiles.length === 0) {
      return {
        success: true,
        result: {
          filePath,
          message: 'No test files found for this file.',
          testFiles: []
        }
      };
    }
    
    // Determine the test runner
    let testCommand = '';
    
    // Check for Jest
    try {
      await execAsync('npx jest --version');
      testCommand = `npx jest ${testFiles.map(f => `"${f}"`).join(' ')}`;
    } catch {
      // Check for Mocha
      try {
        await execAsync('npx mocha --version');
        testCommand = `npx mocha ${testFiles.map(f => `"${f}"`).join(' ')}`;
      } catch {
        return {
          success: false,
          message: 'No suitable test runner found. Please install jest or mocha.'
        };
      }
    }
    
    // Run the tests
    try {
      const { stdout } = await execAsync(testCommand);
      return {
        success: true,
        result: {
          filePath,
          testFiles,
          testOutput: stdout
        }
      };
    } catch (error) {
      // Tests failed
      return {
        success: false,
        result: {
          filePath,
          testFiles,
          testOutput: (error as any).stdout || (error as any).stderr || (error as any).message,
          message: 'Tests failed'
        }
      };
    }
  } catch (error) {
    console.error(`Error testing ${filePath}:`, error);
    throw error;
  }
}

/**
 * Register the debugging tool in the tool registry
 */
export function registerDebuggingTool(): void {
  // This would be called in the tool registry initialization
  // toolRegistry.register(debuggingTool);
} 