import { z } from 'zod';
export type ProviderType = 'openai' | 'deepseek' | 'ollama';
export declare const ConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["openai", "deepseek", "ollama"]>;
    apiKey: z.<PERSON><z.ZodString>;
    baseURL: z.Zod<PERSON>ptional<z.ZodString>;
    model: z.ZodString;
}, "strip", z.Zod<PERSON>ypeAny, {
    provider: "openai" | "deepseek" | "ollama";
    model: string;
    apiKey?: string | undefined;
    baseURL?: string | undefined;
}, {
    provider: "openai" | "deepseek" | "ollama";
    model: string;
    apiKey?: string | undefined;
    baseURL?: string | undefined;
}>;
export type Config = z.infer<typeof ConfigSchema>;
/**
 * Saves the config to disk
 */
export declare function saveConfig(config: Config): Promise<void>;
/**
 * Gets the current config, loading it from disk or creating a default
 */
export declare function getConfig(): Promise<Config>;
/**
 * Updates the provider configuration
 */
export declare function updateProviderConfig(provider: ProviderType, apiKey?: string, baseURL?: string, model?: string): Promise<Config>;
