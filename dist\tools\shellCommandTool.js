import { exec } from 'child_process';
import { promisify } from 'util';
const execPromise = promisify(exec);
/**
 * Gets the shell command tool definition
 */
export function getShellCommandTool() {
    return {
        name: 'shell_command',
        description: 'Execute a shell command on the system',
        parameters: {
            type: 'object',
            properties: {
                command: {
                    type: 'string',
                    description: 'The shell command to execute'
                },
                workingDir: {
                    type: 'string',
                    description: 'The directory to execute the command in (optional)'
                }
            },
            required: ['command']
        },
        execute: executeShellCommand
    };
}
/**
 * Executes a shell command
 */
async function executeShellCommand(args) {
    try {
        const options = {};
        // Set the working directory if provided
        if (args.workingDir) {
            options.cwd = args.workingDir;
        }
        // Execute the command
        const { stdout, stderr } = await execPromise(args.command, options);
        return {
            stdout: stdout.toString().trim(),
            stderr: stderr.toString().trim(),
            exitCode: 0
        };
    }
    catch (error) {
        // If the command fails, exec throws an error with the exit code
        return {
            stdout: error.stdout ? error.stdout.toString().trim() : '',
            stderr: error.stderr ? error.stderr.toString().trim() : error.message,
            exitCode: error.code || 1
        };
    }
}
//# sourceMappingURL=shellCommandTool.js.map