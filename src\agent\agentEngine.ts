import { processUserInput } from './inputProcessor.js';
import { executeToolCall } from '../tools/toolExecutor.js';
import { generateAgentResponse } from '../ai/provider.js';
import { ContextManager } from '../context/contextManager.js';
import { renderAgentResponse, renderStreamingContent, renderToolExecution, LoadingSpinner, clearLine } from '../ui/terminalUI.js';
import { initializeSession } from './sessionManager.js';
import chalk from 'chalk';

/**
 * The main agent engine that orchestrates the entire workflow
 */
export async function startAgent(input: string): Promise<void> {
  try {
    // Initialize the session and context
    const session = await initializeSession();
    const contextManager = new ContextManager(session);
    
    // Process the user input and get context
    const processedInput = await processUserInput(input);
    const context = await contextManager.getCurrentContext();
    
    // Start the thinking spinner
    const spinner = new LoadingSpinner('Thinking');
    spinner.start();
    
    // Generate the initial AI response with streaming
    try {
      await generateAgentResponse(
        {
          input: processedInput,
          context,
          tools: session.availableTools
        },
        {
          // Called when content starts streaming
          onContent: (content) => {
            // Stop the spinner on first content
            if (spinner.isSpinning()) {
              spinner.stop();
            }
            
            // Render the streaming content
            renderStreamingContent(content);
          },
          
          // Called when a tool call is detected
          onToolCall: async (toolCall) => {
            // Add a new line after content
            console.log('\n');
            
            // Render the tool execution message
            renderToolExecution(toolCall.name);
            
            try {
              // Execute the tool call
              const toolResult = await executeToolCall({
                id: toolCall.id,
                name: toolCall.name,
                arguments: toolCall.arguments
              });
              
              // Update the context with the tool result
              await contextManager.updateContext({
                toolCall,
                toolResult
              });
              
              // Clear the line and show the result
              clearLine();
              console.log(chalk.green(`✓ Executed: ${toolCall.name}`));
              
              // If the tool execution was successful, display the result in a condensed format
              if (toolResult.success) {
                // Only show the result if it's not too large
                const resultString = JSON.stringify(toolResult.result);
                if (resultString.length < 1000) {
                  console.log(chalk.gray(`  Result: ${resultString}`));
                } else {
                  console.log(chalk.gray('  Result: [Large output]'));
                }
              } else {
                // Show the error if the tool execution failed
                console.log(chalk.red(`  Error: ${toolResult.error}`));
              }
              
            } catch (error) {
              clearLine();
              console.error(chalk.red(`✗ Failed to execute ${toolCall.name}: ${error}`));
            }
          },
          
          // Called when there's an error
          onError: (error) => {
            spinner.stop();
            console.error(chalk.red('Error generating response:'), error.message);
          },
          
          // Called when the response is complete
          onComplete: async (response) => {
            // If there was no content but there were tool calls, show a message
            if (!response.content && response.toolCalls && response.toolCalls.length > 0) {
              console.log(chalk.gray('\nCompleting your request...'));
            }
            
            // At this point, we've already processed all the tool calls in the onToolCall callback
            // The context has been updated with each tool result
            
            // Continue the conversation autonomously if needed
            if (response.toolCalls && response.toolCalls.length > 0) {
              // Get the updated context after all tool executions
              const updatedContext = await contextManager.getCurrentContext();
              
              // Start a new thinking spinner
              const continueSpinner = new LoadingSpinner('Thinking');
              continueSpinner.start();
              
              // Generate a follow-up response
              await generateAgentResponse(
                {
                  input: processedInput,
                  context: updatedContext,
                  tools: session.availableTools
                },
                {
                  onContent: (content) => {
                    // Stop the spinner on first content
                    if (continueSpinner.isSpinning()) {
                      continueSpinner.stop();
                    }
                    
                    // Add a separator line
                    console.log(chalk.gray('\n---\n'));
                    
                    // Render the streaming content
                    renderStreamingContent(content);
                  },
                  
                  // Continue with the same callbacks
                  onToolCall: async (toolCall) => {
                    // Similar logic to the above onToolCall
                    console.log('\n');
                    renderToolExecution(toolCall.name);
                    
                    try {
                      const toolResult = await executeToolCall({
                        id: toolCall.id,
                        name: toolCall.name,
                        arguments: toolCall.arguments
                      });
                      
                      await contextManager.updateContext({
                        toolCall,
                        toolResult
                      });
                      
                      clearLine();
                      console.log(chalk.green(`✓ Executed: ${toolCall.name}`));
                      
                      const resultString = JSON.stringify(toolResult.result);
                      if (resultString.length < 1000) {
                        console.log(chalk.gray(`  Result: ${resultString}`));
                      } else {
                        console.log(chalk.gray('  Result: [Large output]'));
                      }
                    } catch (error) {
                      clearLine();
                      console.error(chalk.red(`✗ Failed to execute ${toolCall.name}: ${error}`));
                    }
                  },
                  
                  onError: (error) => {
                    continueSpinner.stop();
                    console.error(chalk.red('Error generating follow-up response:'), error.message);
                  },
                  
                  onComplete: async () => {
                    // Persist the session state
                    await session.save();
                  }
                }
              );
            } else {
              // If there were no tool calls, just persist the session
              await session.save();
            }
          }
        }
      );
    } catch (error) {
      spinner.stop();
      console.error(chalk.red('Agent execution error:'), error);
    }
    
  } catch (error) {
    console.error(chalk.red('Error in agent engine:'), error);
    throw error;
  }
} 