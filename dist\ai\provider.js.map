{"version": 3, "file": "provider.js", "sourceRoot": "", "sources": ["../../src/ai/provider.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,KAAK,MAAM,OAAO,CAAC;AA2B1B;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc;IAClC,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;IAEjC,OAAO,IAAI,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,MAAM,CAAC,OAAO;KACxB,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA8B,EAC9B,eAAiC;IAEjC,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;IAEjC,IAAI,CAAC;QACH,2BAA2B;QAC3B,MAAM,MAAM,GAAG,MAAM,cAAc,EAAE,CAAC;QAEtC,iDAAiD;QACjD,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5C,IAAI,EAAE,UAAmB;YACzB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;SACF,CAAC,CAAC,CAAC;QAEJ,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAEvC,yEAAyE;QACzE,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,MAAM,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;QACrG,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ;gBACR,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAEpD,4BAA4B;YAC5B,OAAO;gBACL,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,EAAE;gBACtC,SAAS,EAAE,eAAe,CAAC,UAAU;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,kCAAkC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEpE,IAAI,eAAe,EAAE,OAAO,EAAE,CAAC;YAC7B,eAAe,CAAC,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC;QAED,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB,CACpC,MAAc,EACd,QAAe,EACf,KAAY,EACZ,KAAa,EACb,SAA0B;IAE1B,uCAAuC;IACvC,IAAI,kBAAkB,GAAG,EAAE,CAAC;IAC5B,IAAI,oBAAoB,GAAU,EAAE,CAAC;IACrC,IAAI,eAAe,GAAQ,IAAI,CAAC;IAEhC,IAAI,CAAC;QACH,+BAA+B;QAC/B,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD,KAAK;YACL,QAAQ;YACR,KAAK;YACL,WAAW,EAAE,GAAG;YAChB,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACjC,4BAA4B;YAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;gBACrC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC/C,kBAAkB,IAAI,OAAO,CAAC;gBAE9B,4BAA4B;gBAC5B,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;oBACxB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;gBACxC,KAAK,MAAM,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;oBAC9D,gDAAgD;oBAChD,IAAI,aAAa,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;wBACtC,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;4BAC/C,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG;gCAC1C,EAAE,EAAE,EAAE;gCACN,IAAI,EAAE,UAAU;gCAChB,QAAQ,EAAE;oCACR,IAAI,EAAE,EAAE;oCACR,SAAS,EAAE,EAAE;iCACd;6BACF,CAAC;4BAEF,eAAe,GAAG,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC;oBAED,0BAA0B;oBAC1B,IAAI,aAAa,CAAC,EAAE,EAAE,CAAC;wBACrB,eAAe,CAAC,EAAE,IAAI,aAAa,CAAC,EAAE,CAAC;oBACzC,CAAC;oBAED,2BAA2B;oBAC3B,IAAI,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;wBACjC,eAAe,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAC/D,CAAC;oBAED,gCAAgC;oBAChC,IAAI,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;wBACtC,eAAe,CAAC,QAAQ,CAAC,SAAS,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC;wBAEvE,2DAA2D;wBAC3D,IAAI,CAAC;4BACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAE5D,0EAA0E;4BAC1E,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;gCACzB,MAAM,QAAQ,GAAG;oCACf,EAAE,EAAE,eAAe,CAAC,EAAE;oCACtB,IAAI,EAAE,eAAe,CAAC,QAAQ,CAAC,IAAI;oCACnC,SAAS,EAAE,IAAI;iCAChB,CAAC;gCAEF,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;4BACjC,CAAC;wBACH,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,sDAAsD;wBACxD,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,MAAM,aAAa,GAAkB;YACnC,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS;SAC9E,CAAC;QAEF,6BAA6B;QAC7B,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,8BAA8B,CAAC,EAAE,KAAK,CAAC,CAAC;QAEhE,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,MAA8B;IACnD,MAAM,QAAQ,GAAG;QACf;YACE,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE;;;;;;;;6KAQ8J;SACxK;QACD;YACE,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAM,CAAC,KAAK;SACtB;KACF,CAAC;IAEF,iDAAiD;IACjD,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxF,8DAA8D;QAC9D,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;YAC5E,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,sEAAsE;YACtE,GAAG,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC;SACpD,CAAC,CAAC,CAAC;QAEJ,iDAAiD;QACjD,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,CAAC,CAAC;IAC5C,CAAC;IAED,+BAA+B;IAC/B,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC9B,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAe;YACrB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YACzD,YAAY,EAAE,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;SAC7C,CAAC,CAAC,CAAC,iDAAiD;IAC9D,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC"}