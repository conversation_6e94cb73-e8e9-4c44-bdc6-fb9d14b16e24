/**
 * Renders the agent response in the terminal
 */
export declare function renderAgentResponse(content: string): Promise<void>;
/**
 * Renders streaming content in the terminal
 * This is optimized for displaying content as it arrives
 */
export declare function renderStreamingContent(content: string): void;
/**
 * Renders a message indicating tool execution
 */
export declare function renderToolExecution(toolName: string): void;
/**
 * Clears the current line in the terminal
 */
export declare function clearLine(): void;
/**
 * Renders a diff view for file changes
 */
export declare function renderDiff(originalContent: string, newContent: string, fileName: string): void;
/**
 * Renders a loading spinner in the terminal
 */
export declare class LoadingSpinner {
    private frames;
    private interval;
    private currentFrame;
    private text;
    private spinning;
    constructor(text?: string);
    start(): void;
    stop(): void;
    updateText(text: string): void;
    isSpinning(): boolean;
}
/**
 * Renders a success message
 */
export declare function renderSuccess(message: string): void;
/**
 * Renders an error message
 */
export declare function renderError(message: string): void;
/**
 * Renders a warning message
 */
export declare function renderWarning(message: string): void;
/**
 * Renders a semantic code search result
 */
export declare function renderSearchResult(fileName: string, lineNumber: number, snippet: string, relevance: number): void;
