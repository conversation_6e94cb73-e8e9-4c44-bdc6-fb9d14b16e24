/**
 * A simple test that doesn't require imports
 */
async function simpleTest() {
    console.log('Running simple test...');
    // Simple test that should pass
    const result = 2 + 2;
    if (result === 4) {
        console.log('✅ Test passed: 2 + 2 = 4');
    }
    else {
        console.error('❌ Test failed: 2 + 2 =', result);
    }
}
// Run the test
simpleTest().catch(error => {
    console.error('Test error:', error);
});
export {};
//# sourceMappingURL=simple.test.js.map