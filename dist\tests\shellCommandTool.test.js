import { getShellCommandTool } from '../tools/shellCommandTool.js';
/**
 * Test the shell command tool
 */
async function testShellCommandTool() {
    console.log('Testing shell command tool...');
    // Get the tool
    const tool = getShellCommandTool();
    // Execute a simple command
    const result = await tool.execute({ command: 'echo "Hello, Agent!"' });
    // Check the result
    if (result.stdout === 'Hello, Agent!' && result.exitCode === 0) {
        console.log('✅ Test passed');
    }
    else {
        console.error('❌ Test failed');
        console.error('Expected: "Hello, Agent!", got:', result.stdout);
    }
}
// Run the test
testShellCommandTool().catch(error => {
    console.error('Test error:', error);
});
//# sourceMappingURL=shellCommandTool.test.js.map