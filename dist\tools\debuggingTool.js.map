{"version": 3, "file": "debuggingTool.js", "sourceRoot": "", "sources": ["../../src/tools/debuggingTool.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,MAAM,aAAa,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAElC;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAmB;IAC3C,IAAI,EAAE,WAAW;IACjB,WAAW,EAAE,6CAA6C;IAC1D,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,2BAA2B;aACzC;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,oCAAoC;gBACjD,IAAI,EAAE;oBACJ,SAAS;oBACT,MAAM;oBACN,KAAK;oBACL,MAAM;iBACP;aACF;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,qDAAqD;aACnE;SACF;QACD,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;KACpC;IAED,KAAK,CAAC,OAAO,CAAC,IAIb;QACC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE5D,2BAA2B;YAC3B,IAAI,CAAC;gBACH,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,mBAAmB,IAAI,CAAC,QAAQ,EAAE;iBAC5C,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;gBACvB,KAAK,SAAS;oBACZ,OAAO,MAAM,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAErC,KAAK,MAAM;oBACT,OAAO,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAElC,KAAK,KAAK;oBACR,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;wBACzB,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,+CAA+C;yBACzD,CAAC;oBACJ,CAAC;oBACD,OAAO,MAAM,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAEtD,KAAK,MAAM;oBACT,OAAO,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAElC;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,gCAAgC,IAAI,CAAC,SAAS,EAAE;qBAC1D,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,KAAK,UAAU,WAAW,CAAC,QAAgB;IACzC,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAE3D,iBAAiB;QACjB,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,EAAW;YACnB,WAAW,EAAE,EAAc;SAC5B,CAAC;QAEF,6CAA6C;QAC7C,IAAI,aAAa,KAAK,KAAK,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YACvD,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC3D,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,YAAY;oBAClB,OAAO,EAAE,SAAS,iBAAiB,CAAC,MAAM,qEAAqE;oBAC/G,KAAK,EAAE,iBAAiB,CAAC,MAAM;iBAChC,CAAC,CAAC;YACL,CAAC;YAED,0BAA0B;YAC1B,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9D,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,SAAS,WAAW,CAAC,MAAM,0CAA0C;oBAC9E,KAAK,EAAE,WAAW,CAAC,MAAM;iBAC1B,CAAC,CAAC;YACL,CAAC;YAED,wDAAwD;YACxD,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC9E,IAAI,eAAe,EAAE,CAAC;gBACpB,KAAK,MAAM,WAAW,IAAI,eAAe,EAAE,CAAC;oBAC1C,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;oBAChE,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,gBAAgB,OAAO,eAAe,EAAE,GAAG,CAAC,CAAC;oBACzE,MAAM,UAAU,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBAE1D,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC,CAAC,uBAAuB;wBAC5C,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;4BAClB,IAAI,EAAE,iBAAiB;4BACvB,OAAO,EAAE,YAAY,OAAO,kBAAkB;4BAC9C,QAAQ,EAAE,OAAO;yBAClB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBACjF,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAC5E,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE;gBACN,QAAQ;gBACR,QAAQ,EAAE,aAAa;gBACvB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;gBACjC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC;SACF,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,QAAQ,CAAC,QAAgB;IACtC,IAAI,CAAC;QACH,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3D,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,2DAA2D;QAC3D,IAAI,aAAa,KAAK,KAAK,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YACvD,+BAA+B;YAC/B,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,sBAAsB,CAAC,CAAC;gBACxC,WAAW,GAAG,eAAe,QAAQ,iBAAiB,CAAC;YACzD,CAAC;YAAC,MAAM,CAAC;gBACP,8DAA8D;gBAC9D,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;oBAC5B,IAAI,CAAC;wBACH,MAAM,SAAS,CAAC,mBAAmB,CAAC,CAAC;wBACrC,WAAW,GAAG,YAAY,QAAQ,YAAY,CAAC;oBACjD,CAAC;oBAAC,MAAM,CAAC;wBACP,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,gEAAgE;yBAC1E,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,mEAAmE;qBAC7E,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,2BAA2B,aAAa,SAAS;aAC3D,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,CAAC;YAEhD,oBAAoB;YACpB,IAAI,WAAW,CAAC;YAChB,IAAI,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC1C,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;YACnC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ;oBACR,WAAW;iBACZ;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iDAAiD;YACjD,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC1D,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE;wBACN,QAAQ;wBACR,WAAW,EAAE;4BACX,MAAM,EAAG,KAAa,CAAC,MAAM,IAAK,KAAa,CAAC,OAAO;yBACxD;qBACF;iBACF,CAAC;YACJ,CAAC;YAED,yDAAyD;YACzD,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY,KAAK,IAAK,KAAa,CAAC,MAAM,EAAE,CAAC;gBACtF,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAE,KAAa,CAAC,MAAM,CAAC,CAAC;oBACtD,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE;4BACN,QAAQ;4BACR,WAAW;yBACZ;qBACF,CAAC;gBACJ,CAAC;gBAAC,MAAM,CAAC;oBACP,4BAA4B;gBAC9B,CAAC;YACH,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,OAAO,CAAC,QAAgB,EAAE,cAAsB;IAC7D,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAErD,iEAAiE;QACjE,uDAAuD;QACvD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,MAAM,EAAE;gBACN,QAAQ;gBACR,OAAO;gBACP,cAAc;gBACd,OAAO,EAAE,sGAAsG;aAChH;SACF,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAClD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,QAAQ,CAAC,QAAgB;IACtC,IAAI,CAAC;QACH,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEzC,2CAA2C;QAC3C,MAAM,gBAAgB,GAAG;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,QAAQ,aAAa,EAAE,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,QAAQ,aAAa,EAAE,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,GAAG,QAAQ,QAAQ,aAAa,EAAE,CAAC;YACrE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,GAAG,QAAQ,QAAQ,aAAa,EAAE,CAAC;YACrE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,QAAQ,QAAQ,aAAa,EAAE,CAAC;YACrE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,QAAQ,QAAQ,aAAa,EAAE,CAAC;SACtE,CAAC;QAEF,2BAA2B;QAC3B,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,MAAM,WAAW,IAAI,gBAAgB,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACH,MAAM,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC7B,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC;YAAC,MAAM,CAAC;gBACP,+BAA+B;YACjC,CAAC;QACH,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ;oBACR,OAAO,EAAE,oCAAoC;oBAC7C,SAAS,EAAE,EAAE;iBACd;aACF,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,iBAAiB;QACjB,IAAI,CAAC;YACH,MAAM,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACtC,WAAW,GAAG,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACrE,CAAC;QAAC,MAAM,CAAC;YACP,kBAAkB;YAClB,IAAI,CAAC;gBACH,MAAM,SAAS,CAAC,qBAAqB,CAAC,CAAC;gBACvC,WAAW,GAAG,aAAa,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACtE,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,8DAA8D;iBACxE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ;oBACR,SAAS;oBACT,UAAU,EAAE,MAAM;iBACnB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAe;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE;oBACN,QAAQ;oBACR,SAAS;oBACT,UAAU,EAAG,KAAa,CAAC,MAAM,IAAK,KAAa,CAAC,MAAM,IAAK,KAAa,CAAC,OAAO;oBACpF,OAAO,EAAE,cAAc;iBACxB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB;IACnC,2DAA2D;IAC3D,wCAAwC;AAC1C,CAAC"}