import * as fs from 'fs/promises';
import * as path from 'path';
import { glob } from 'glob';
import { renderSearchResult } from '../ui/terminalUI.js';
import { ToolDefinition } from './types.js';

/**
 * A simple semantic code search tool
 * In a real implementation, this would use embeddings and vector similarity
 */
export const semanticSearchTool: ToolDefinition = {
  name: 'semanticCodeSearch',
  description: 'Search code semantically by meaning rather than just text matching',
  parameters: {
    type: 'object',
    properties: {
      query: {
        type: 'string',
        description: 'The semantic query to search for'
      },
      filePattern: {
        type: 'string',
        description: 'Optional glob pattern to filter files (e.g., "**/*.ts" for TypeScript files)'
      },
      maxResults: {
        type: 'number',
        description: 'Maximum number of results to return'
      }
    },
    required: ['query']
  },
  
  async execute(args: { query: string; filePattern?: string; maxResults?: number }): Promise<any> {
    try {
      // Default values
      const filePattern = args.filePattern || '**/*.{ts,js,tsx,jsx}';
      const maxResults = args.maxResults || 5;
      
      // Get the current working directory
      const cwd = process.cwd();
      
      // Find all files matching the pattern
      const files = await glob(filePattern, { 
        ignore: ['**/node_modules/**', '**/dist/**', '**/build/**'],
        cwd
      });
      
      // Results array
      const results: Array<{
        file: string;
        lineNumber: number;
        snippet: string;
        relevance: number;
      }> = [];
      
      // Process each file
      for (const file of files) {
        const filePath = path.join(cwd, file);
        
        try {
          // Read the file content
          const content = await fs.readFile(filePath, 'utf-8');
          const lines = content.split('\n');
          
          // In a real implementation, we would use embeddings and vector similarity here
          // For now, we'll just do a simple keyword search with some basic relevance scoring
          
          // Split the query into keywords
          const keywords = args.query.toLowerCase().split(/\s+/).filter(k => k.length > 2);
          
          // Process each line
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineLower = line.toLowerCase();
            
            // Calculate a simple relevance score based on keyword matches
            let relevance = 0;
            for (const keyword of keywords) {
              if (lineLower.includes(keyword)) {
                relevance += 0.2;
              }
            }
            
            // If we have some relevance, add context lines and add to results
            if (relevance > 0) {
              // Get some context lines
              const startLine = Math.max(0, i - 2);
              const endLine = Math.min(lines.length - 1, i + 2);
              
              // Create the snippet with context
              const snippet = lines.slice(startLine, endLine + 1).join('\n');
              
              // Add to results
              results.push({
                file,
                lineNumber: i + 1, // 1-indexed line numbers
                snippet,
                relevance
              });
            }
          }
        } catch (error) {
          console.error(`Error processing file ${file}:`, error);
        }
      }
      
      // Sort by relevance
      results.sort((a, b) => b.relevance - a.relevance);
      
      // Limit results
      const limitedResults = results.slice(0, maxResults);
      
      // Display the results
      if (limitedResults.length === 0) {
        return { results: [], message: 'No results found for the semantic query.' };
      }
      
      // Display the results in the terminal
      console.log(`\nSemantic search results for: "${args.query}"\n`);
      for (const result of limitedResults) {
        renderSearchResult(result.file, result.lineNumber, result.snippet, result.relevance);
      }
      
      return {
        results: limitedResults,
        message: `Found ${limitedResults.length} results for the semantic query.`
      };
    } catch (error) {
      console.error('Error in semantic code search:', error);
      throw error;
    }
  }
};

/**
 * Register the semantic search tool in the tool registry
 */
export function registerSemanticSearchTool(): void {
  // This would be called in the tool registry initialization
  // toolRegistry.register(semanticSearchTool);
} 