import { getAvailableTools } from '../tools/toolRegistry.js';
/**
 * Initializes a new session for the agent
 */
export async function initializeSession() {
    // Generate a unique session ID
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    // Get the available tools for this session
    const tools = await getAvailableTools();
    // Create and return the session object
    const session = {
        id: sessionId,
        startTime: new Date(),
        availableTools: tools,
        conversationHistory: [],
        save: async () => {
            // In a real implementation, this would persist the session state
            // For now, we'll just log that we're saving
            console.log(`Saving session ${sessionId}`);
        }
    };
    return session;
}
//# sourceMappingURL=sessionManager.js.map