import { getTool } from './toolRegistry.js';
import { LoadingSpinner } from '../ui/terminalUI.js';
/**
 * Executes a tool call from the AI
 */
export async function executeToolCall(toolCall) {
    try {
        // Get the tool definition
        const tool = getTool(toolCall.name);
        if (!tool) {
            return {
                success: false,
                result: null,
                error: `Tool not found: ${toolCall.name}`
            };
        }
        console.log(`Executing tool: ${toolCall.name}`);
        // Parse the arguments
        const args = parseToolArguments(toolCall);
        // Show a loading spinner
        const spinner = new LoadingSpinner(`Executing ${toolCall.name}`);
        spinner.start();
        try {
            // Execute the tool
            const result = await tool.execute(args);
            // Return the result
            return {
                success: true,
                result
            };
        }
        catch (error) {
            console.error(`Error executing tool ${toolCall.name}:`, error);
            return {
                success: false,
                result: null,
                error: error instanceof Error ? error.message : String(error)
            };
        }
        finally {
            // Stop the spinner
            spinner.stop();
        }
    }
    catch (error) {
        console.error('Error in executeToolCall:', error);
        return {
            success: false,
            result: null,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
/**
 * Parses the arguments from a tool call
 */
function parseToolArguments(toolCall) {
    try {
        // If arguments is a string, try to parse it as JSON
        if (typeof toolCall.arguments === 'string') {
            return JSON.parse(toolCall.arguments);
        }
        // Otherwise, return the arguments as is
        return toolCall.arguments;
    }
    catch (error) {
        console.error('Error parsing tool arguments:', error);
        throw error;
    }
}
/**
 * Executes multiple tool calls in sequence
 */
export async function executeToolChain(toolCalls) {
    const results = [];
    for (const toolCall of toolCalls) {
        const result = await executeToolCall(toolCall);
        results.push(result);
        // If a tool fails, stop executing
        if (!result.success) {
            break;
        }
    }
    return results;
}
//# sourceMappingURL=toolExecutor.js.map