import * as fs from 'fs/promises';
import * as path from 'path';
import { glob } from 'glob';
import { renderDiff } from '../ui/terminalUI.js';
/**
 * Gets all file operation tools
 */
export async function getFileOperationTools() {
    return [
        getReadFileTool(),
        getWriteFileTool(),
        getListDirectoryTool(),
        getSearchFilesTool(),
        getDeleteFileTool(),
    ];
}
/**
 * Gets the read file tool definition
 */
function getReadFileTool() {
    return {
        name: 'read_file',
        description: 'Read the contents of a file',
        parameters: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'The path to the file to read'
                }
            },
            required: ['filePath']
        },
        execute: async (args) => {
            try {
                const content = await fs.readFile(args.filePath, 'utf-8');
                return { content };
            }
            catch (error) {
                throw new Error(`Failed to read file: ${args.filePath}, ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    };
}
/**
 * Gets the write file tool definition
 */
function getWriteFileTool() {
    return {
        name: 'write_file',
        description: 'Write content to a file',
        parameters: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'The path to the file to write'
                },
                content: {
                    type: 'string',
                    description: 'The content to write to the file'
                },
                showDiff: {
                    type: 'boolean',
                    description: 'Whether to show a diff of the changes (default: true)'
                }
            },
            required: ['filePath', 'content']
        },
        execute: async (args) => {
            try {
                // Create the directory if it doesn't exist
                const dirPath = path.dirname(args.filePath);
                await fs.mkdir(dirPath, { recursive: true });
                // Read the original content if the file exists and diff is enabled
                let originalContent = '';
                const showDiff = args.showDiff !== false; // Default to true
                try {
                    if (showDiff) {
                        originalContent = await fs.readFile(args.filePath, 'utf-8');
                    }
                }
                catch (error) {
                    // File doesn't exist, that's fine
                }
                // Write the new content
                await fs.writeFile(args.filePath, args.content);
                // Show the diff if requested
                if (showDiff && originalContent !== args.content) {
                    renderDiff(originalContent, args.content, args.filePath);
                }
                return { success: true, filePath: args.filePath };
            }
            catch (error) {
                throw new Error(`Failed to write file: ${args.filePath}, ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    };
}
/**
 * Gets the list directory tool definition
 */
function getListDirectoryTool() {
    return {
        name: 'list_directory',
        description: 'List the contents of a directory',
        parameters: {
            type: 'object',
            properties: {
                dirPath: {
                    type: 'string',
                    description: 'The path to the directory to list'
                }
            },
            required: ['dirPath']
        },
        execute: async (args) => {
            try {
                const entries = await fs.readdir(args.dirPath, { withFileTypes: true });
                // Group entries by type
                const files = [];
                const directories = [];
                for (const entry of entries) {
                    if (entry.isDirectory()) {
                        directories.push(entry.name);
                    }
                    else {
                        files.push(entry.name);
                    }
                }
                return { directories, files };
            }
            catch (error) {
                throw new Error(`Failed to list directory: ${args.dirPath}, ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    };
}
/**
 * Gets the search files tool definition
 */
function getSearchFilesTool() {
    return {
        name: 'search_files',
        description: 'Search for files matching a pattern',
        parameters: {
            type: 'object',
            properties: {
                pattern: {
                    type: 'string',
                    description: 'The glob pattern to search for'
                },
                basePath: {
                    type: 'string',
                    description: 'The base directory to search in (default: current directory)'
                }
            },
            required: ['pattern']
        },
        execute: async (args) => {
            try {
                const basePath = args.basePath || process.cwd();
                const matches = await glob(args.pattern, { cwd: basePath });
                return { matches, basePath };
            }
            catch (error) {
                throw new Error(`Failed to search files: ${args.pattern}, ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    };
}
/**
 * Gets the delete file tool definition
 */
function getDeleteFileTool() {
    return {
        name: 'delete_file',
        description: 'Delete a file',
        parameters: {
            type: 'object',
            properties: {
                filePath: {
                    type: 'string',
                    description: 'The path to the file to delete'
                }
            },
            required: ['filePath']
        },
        execute: async (args) => {
            try {
                await fs.unlink(args.filePath);
                return { success: true, filePath: args.filePath };
            }
            catch (error) {
                throw new Error(`Failed to delete file: ${args.filePath}, ${error instanceof Error ? error.message : String(error)}`);
            }
        }
    };
}
//# sourceMappingURL=fileOperationTools.js.map