import { ToolDefinition } from '../tools/types.js';
/**
 * Represents a session for the agent
 */
export interface AgentSession {
    id: string;
    startTime: Date;
    availableTools: ToolDefinition[];
    conversationHistory: ConversationMessage[];
    save: () => Promise<void>;
}
/**
 * Represents a message in the conversation history
 */
export interface ConversationMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    toolCalls?: any[];
    toolResults?: any[];
}
/**
 * Initializes a new session for the agent
 */
export declare function initializeSession(): Promise<AgentSession>;
