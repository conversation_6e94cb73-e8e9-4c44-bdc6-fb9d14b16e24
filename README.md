# Autonomous Agent CLI Tool

A fully autonomous, AI-powered CLI tool that operates in the user's local environment (Windows, macOS, and Linux).

## Features

- **Fully Autonomous**: Executes commands without approval checkpoints
- **AI-Powered**: Integrates with OpenAI, Deepseek, and Ollama
- **Local Operation**: Operates in the user's local environment
- **Tool System**: Shell commands, file operations, and more
- **Context Management**: Maintains context across sessions
- **Diff Visualization**: Visual representation of changes
- **Semantic Code Search**: Find code by meaning rather than just text matching
- **Git Integration**: Perform git operations like commit, branch, status, and more
- **Debugging Tools**: Analyze and fix common issues in code

## Installation

### Prerequisites

- Node.js v18 or higher
- npm or yarn

### Install from Source

```bash
# Clone the repository
git clone https://github.com/yourusername/autonomous-agent-cli.git
cd autonomous-agent-cli

# Install dependencies
npm install

# Build the project
npm run build

# Install globally
npm install -g .
```

## Usage

### Configuration

First, initialize the agent with your API key:

```bash
agent init
```

This will guide you through setting up your preferred AI provider (OpenAI, Deepseek, or Ollama) and API key.

### Running the Agent

Run the agent with a prompt:

```bash
agent run "Your task or question here"
```

### Examples

```bash
# Ask the agent to create a new file
agent run "Create a new React component called Button"

# Ask the agent to analyze code
agent run "Find all TODO comments in the codebase"

# Ask the agent to execute system tasks
agent run "Check if port 3000 is in use and kill the process if it is"

# Ask the agent to perform git operations
agent run "Create a new branch called feature/login and commit my changes"

# Ask the agent to debug code
agent run "Find and fix any issues in src/components/App.js"

# Ask the agent to search code semantically
agent run "Find all code related to authentication in the project"
```

## Architecture

The tool is built with a modular architecture:

- **Agent Engine**: Central orchestrator that manages the entire workflow
- **Tool System**: Registry of available tools and their schemas
- **Context Manager**: Maintains and updates context across sessions
- **AI Provider**: Handles AI integration with multiple providers
- **Terminal UI**: Rich interactive terminal UI with streaming responses
- **Semantic Search**: Deep semantic comprehension of code structure and purpose
- **Git Operations**: Full suite of version control operations
- **Debugging System**: Runtime error detection, analysis, and automated fixing

## Key Differences from Approval-Based Systems

1. **No Approval Checkpoints**: All tool calls execute immediately
2. **Continuous Flow**: No interruptions in the execution process
3. **Full Environment Access**: Direct access to shell and filesystem
4. **Autonomous Decision Making**: AI determines and executes next steps
5. **Complete Task Chains**: Full tasks completed without user confirmation

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. 